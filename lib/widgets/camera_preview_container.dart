import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_inner_shadow/flutter_inner_shadow.dart';
import 'package:camera/camera.dart';
import 'package:fujimo/controllers/camera-cubit/camera_cubit.dart';
import 'package:fujimo/services/camera_service.dart';
import 'package:fujimo/widgets/dispresion_radius_widget.dart';
import 'package:fujimo/widgets/dispresion_strengh_widget.dart';
import 'package:fujimo/widgets/iso_widget.dart';
import 'package:fujimo/widgets/shutter_speed_widget.dart';

enum CameraSlider {
  shutterSpeed,
}

class CameraPreviewContainer extends StatelessWidget {
  const CameraPreviewContainer({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(
        //top: 170,
        right: 0,
        left: 10,
        bottom: 15,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(4),
        child: InnerShadow(
          shadows: const [
            BoxShadow(
              color: Color(0x80FFFFFF),
              blurRadius: 2,
            ),
          ],
          child: Container(
            width: double.infinity,
            height: double.infinity,
            decoration: const BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(4)),
              gradient: LinearGradient(
                stops: [0, 21, 100],
                begin: Alignment.centerRight,
                end: Alignment.centerLeft,
                colors: [
                  Color(0xFF212121),
                  Color(0xFF1A1A1C),
                  Color(0xFF313133)
                ],
              ),
            ),
            child: _CameraPreviewContent(),
          ),
        ),
      ),
    );
  }
}

class _CameraPreviewContent extends StatefulWidget {
  @override
  State<_CameraPreviewContent> createState() => _CameraPreviewContentState();
}

class _CameraPreviewContentState extends State<_CameraPreviewContent> {
  CameraSlider selectedSlider = CameraSlider.shutterSpeed;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      margin: const EdgeInsets.symmetric(
        vertical: 20,
        horizontal: 10,
      ),
      decoration: BoxDecoration(
        border: Border.all(
          color: Colors.grey.withValues(alpha: .5),
          width: .5,
        ),
        borderRadius: const BorderRadius.all(Radius.circular(4)),
        color: Colors.black,
      ),
      child: BlocConsumer<CameraCubit, CameraState>(
        buildWhen: (previous, current) => current is CameraConfiguration,
        listener: (context, state) {},
        builder: (context, state) {
          final configs = state as CameraConfiguration;
          if (!configs.isInitialized) {
            return Container();
          }

          if (configs.selectedCamera == SelectedCamera.back) {
            return Stack(
              fit: StackFit.expand,
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(4),
                  child: AspectRatio(
                    aspectRatio: configs.cameraController!.value.aspectRatio,
                    //child: CameraPreview(configs.cameraController!),
                  ),
                ),
                Positioned(
                  left: 0,
                  bottom: 0,
                  top: 0,
                  child: Row(
                    children: [
                      Container(
                        width: 35,
                        decoration: BoxDecoration(
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(4),
                            bottomLeft: Radius.circular(4),
                          ),
                          color: Colors.black.withValues(alpha: .8),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          spacing: 18,
                          children: [
                            GestureDetector(
                              onTap: () {
                                setState(() {
                                  selectedSlider = CameraSlider.shutterSpeed;
                                });
                              },
                              child: const CameraMenuIcon(
                                iconPath:
                                    'assets/camera-components/shutter-speed.png',
                                color: Color.fromARGB(255, 225, 225, 225),
                              ),
                            ),
                            const CameraMenuIcon(
                              iconPath:
                                  'assets/camera-components/iso-menu-icon.png',
                              color: Colors.red,
                            ),
                            const CameraMenuIcon(
                              iconPath:
                                  'assets/camera-components/exposure-compensation.png',
                              color: Color.fromARGB(255, 225, 225, 225),
                            ),
                            const CameraMenuIcon(
                              iconPath: 'assets/camera-components/grid.png',
                              color: Color.fromARGB(255, 225, 225, 225),
                            ),
                            const CameraMenuIcon(
                              iconPath:
                                  'assets/camera-components/menu-icon.png',
                              color: Color.fromARGB(255, 225, 225, 225),
                            ),
                            const CameraMenuIcon(
                              iconPath:
                                  'assets/camera-components/display-menu-icon.png',
                              color: Color.fromARGB(255, 225, 225, 225),
                            ),
                          ],
                        ),
                      ),
                      if (selectedSlider == CameraSlider.shutterSpeed)
                      //  const IsoWidget()
                        // const ShutterSpeedWidget()
                        // const DispresionRadiusWidget()

                        //! fix baseline is not extending to end ticker width
                        const DispresionStrenghWidget()
                    ],
                  ),
                ),
              ],
            );
          } else {
            return Transform(
              alignment: Alignment.center,
              transform: Matrix4.rotationY(math.pi),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(4),
                child: AspectRatio(
                  aspectRatio: configs.cameraController!.value.aspectRatio,
                  // child: CameraPreview(configs.cameraController!),
                ),
              ),
            );
          }
        },
      ),
    );
  }
}

class CameraMenuIcon extends StatelessWidget {
  final double height;
  final String iconPath;
  final Color color;

  const CameraMenuIcon({
    super.key,
    this.height = 18,
    required this.iconPath,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return RotatedBox(
      quarterTurns: 1,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Image.asset(
          iconPath,
          height: height,
          color: color,
          fit: BoxFit.scaleDown,
        ),
      ),
    );
  }
}
