import 'package:flutter/material.dart';
import 'package:fujimo/global/precision_ruler_widget.dart';

class DispresionStrenghWidget extends StatefulWidget {
  const DispresionStrenghWidget({super.key});

  @override
  State<DispresionStrenghWidget> createState() =>
      _DispresionStrenghWidgetState();
}

class _DispresionStrenghWidgetState extends State<DispresionStrenghWidget> {
  double _value = 0.0;

  @override
  Widget build(BuildContext context) {
    return RotatedBox(
      quarterTurns: 1,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 10.0, horizontal: 60),
        child: PrecisionRuler(
          value: _value,
          minValue: -20,
          maxValue: 20,
          height: 30,
          minorTicksPerMajor: 0,
          majorTickInterval: 5,
          majorTickWidth: 10,
          snapInterval: 5,
          movePointer: true,
          showAlternatingMajorTicks: true,
          onChanged: (value) {
            setState(() {
              _value = value.clamp(-20.0, 20.0);
            });
          },
        ),
      ),
    );
  }
}
