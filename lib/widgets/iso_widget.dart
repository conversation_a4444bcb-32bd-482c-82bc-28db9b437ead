import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:fujimo/global/precision_ruler_widget.dart';

class IsoWidget extends StatefulWidget {
  const IsoWidget({super.key});

  @override
  State<IsoWidget> createState() => _IsoWidgetState();
}

/// RULER: PRECISION RULER
/// ADD AUTO WIDGET
/// USE ALTERNET TICKS
/// MIN 45 MAX 8982

class _IsoWidgetState extends State<IsoWidget> {
  //TODO: update value configurations for iso

  double _value = 0.0;
  double _calculatedValue = 0.0;

  final double _increaseRatio = 297.0;
  final double minValue = 0.0;
  final double maxValue = 30.0;

  @override
  Widget build(BuildContext context) {
    return RotatedBox(
      quarterTurns: 1,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 10.0, horizontal: 60),
        child: PrecisionRuler(
          value: _value,
          minValue: minValue,
          maxValue: maxValue,
          height: 30,
          minorTicksPerMajor: 0,
          majorTickInterval: 10,
          snapInterval: 1,
          movePointer: true,
          
          // Add padding to ensure edge ticks render properly
          horizontalPadding: 15.0,
          
          mediumTickHeightRatio: 0.35,
          minorTickHeightRatio: 0.20,
          mediumTickInterval: 5,
          
          // Ensure consistent tick rendering
          forceFullWidth: true,
          referenceRange: maxValue - minValue,
          
          showAlternatingMajorTicks: false,
          showBaseline: false,
          
          mediumTickWidth: 2,
          majorTickWidth: 2,
          minorTickWidth: 2,
          tickBorderRadius: 0,

          onChanged: (value) {
            setState(() {
              _value = value.clamp(minValue, maxValue);
              //TODO: calculate iso value based on _value and _increaseRatio
              _calculatedValue = _value * _increaseRatio;
              log('iso value: $_calculatedValue');
            });
          },
          // startWidget: Container(
          //   margin: const EdgeInsets.symmetric(vertical: 6),
          //   padding: const EdgeInsets.symmetric(horizontal: 3, vertical: 2),
          //   decoration: BoxDecoration(
          //       gradient: const LinearGradient(
          //         begin: Alignment.topCenter,
          //         end: Alignment.bottomCenter,
          //         colors: [
          //           //* active
          //           // Color.fromARGB(255, 255, 201, 126),
          //           // Color.fromARGB(255, 207, 137, 39)

          //           //* disable
          //           Color.fromARGB(255, 218, 218, 218),
          //           Color.fromARGB(255, 122, 122, 122)
          //         ],
          //       ),
          //       border: Border.all(
          //         color: const Color.fromARGB(255, 37, 37, 37),
          //         width: 0.8,
          //       ),
          //       borderRadius: BorderRadius.circular(2)),
          //   child: FittedBox(
          //     child: Center(
          //       child: Stack(
          //         children: [
          //           // Stroke
          //           Text(
          //             'AUTO',
          //             style: TextStyle(
          //               fontFamily: 'Oxanium',
          //               fontSize: 40,
          //               foreground: Paint()
          //                 ..style = PaintingStyle.stroke
          //                 ..strokeWidth = 8
          //                 ..color = Colors.black,
          //             ),
          //           ),
          //           // Fill
          //           const Text(
          //             'AUTO',
          //             style: TextStyle(
          //               fontFamily: 'Oxanium',
          //               fontSize: 40,
          //               color: Colors.white,
          //             ),
          //           ),
          //         ],
          //       ),
          //     ),
          //   ),
          // ),
        ),
      ),
    );
  }
}
