import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:fujimo/global/precision_ruler_widget.dart';

void main() {
  testWidgets('PrecisionRuler renders correctly', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: Center(
            child: PrecisionRuler(
              value: 0.0,
              minValue: -10.0,
              maxValue: 10.0,
              visibleTicks: 21,

            ),
          ),
        ),
      ),
    );

    // Verify that the PrecisionRuler widget is rendered
    expect(find.byType(PrecisionRuler), findsOneWidget);

    // Verify that our RulerWidget is used
    expect(find.byType(RulerWidget), findsOneWidget);
  });
}
